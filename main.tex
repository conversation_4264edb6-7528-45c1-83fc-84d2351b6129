\documentclass[aspectratio=169]{beamer}
\usetheme[compress]{Boadilla} % Boadilla
\usepackage{graphicx}
\usepackage{tikz}
\usepackage{amsmath}
\usepackage{caption}
\usetikzlibrary{backgrounds}
\usepackage[linesnumbered,ruled,vlined]{algorithm2e}
\usefonttheme{default} 
\usepackage{makecell}
\usepackage{subcaption}
\usepackage{dsfont}
\usepackage{booktabs}

\setbeamertemplate{footline}{
  \leavevmode%
  \hbox{%
    \begin{beamercolorbox}[wd=.3\paperwidth,ht=2.25ex,dp=1ex,center]{author in head/foot}%
      \usebeamerfont{author in head/foot}\insertshortauthor{}\\
    \end{beamercolorbox}%
    \begin{beamercolorbox}[wd=.4\paperwidth,ht=2.25ex,dp=1ex,center]{title in head/foot}%
      \usebeamerfont{title in head/foot}\insertshorttitle{}
    \end{beamercolorbox}%
    \begin{beamercolorbox}[wd=.3\paperwidth,ht=2.25ex,dp=1ex,right]{date in head/foot}%
      \usebeamerfont{date in head/foot}\insertshortdate{}\hspace*{2em}
      \insertframenumber{} / \inserttotalframenumber\hspace*{2ex} 
    \end{beamercolorbox}}%
  \vspace{0pt}
}

\makeatletter
\def\inserttitlepagecustom{%
  \begin{center}
    \vspace*{1cm}
    {\usebeamerfont{title}\usebeamercolor[fg]{title}\inserttitle\par}
    \vspace{0.5cm}
    {\usebeamerfont{subtitle}\usebeamercolor[fg]{subtitle}\insertsubtitle\par}
    \vspace{0.5cm}
    {\usebeamerfont{author}\usebeamercolor[fg]{author}\insertauthor\par}
    \vspace{1cm}
    {\usebeamerfont{institute}\usebeamercolor[fg]{institute}\insertinstitute\par}
    \vspace{0.5cm}
    \textit{Under the supervision of:} Pr. Thibaut Vidal \(^1\) \par
    \vspace{0.5cm}
    {\usebeamerfont{date}\usebeamercolor[fg]{date}\insertdate\par}
  \end{center}
}
\addtobeamertemplate{title page}{}{\inserttitlepagecustom}
\makeatother

\title[Pre-doc exam]{Counterfactual Explanations in Trustworthy Machine Learning: Solution Methods and Privacy Vulnerabilities}
\author[Awa Khouna]{Awa Khouna \inst{1}}

\institute[Polytechnique Montréal] % (optional)
{
  \inst{1}% 
  Polytechnique Montréal
}
\date{\today}


\begin{document}
\input{macros}
%%% Outline :
% 1. Background
% 1.1 Decision Trees (explanation of how they work, their interpretability and their geometry)
% 1.2 Counterfactual Explanations (definitions, properties and examples)
% 1.3 Model Extraction Attacks (definition, functional equivalence, fidelity and examples)
% 2. Project 1 : Related work then the proposed method
% 3. Project 2 : Related work then the proposed method
% 4. Project 3 : Related work then the proposed method
% 5. Conclusion

\frame{\inserttitlepagecustom}
\begin{frame}
    \frametitle{Outline}
    \tableofcontents
\end{frame}

\AtBeginSection[]
{
  \begin{frame}
    \frametitle{Outline}
    \tableofcontents[currentsection]
  \end{frame}
}
\section{Background}
\subsection{Decision Trees}
\begin{frame}
  \frametitle{Decision Trees}
  \vspace{-0.2cm}
  \begin{center}
    \input{DT_example.tex}
  \end{center}
\end{frame}

\begin{frame}
  \frametitle{Ensemble of Trees}
  \vspace{-0.2cm}
  \begin{center}
    \input{RF_example.tex}
  \end{center}
\end{frame}


%%% Counterfactual explanations
\subsection{Counterfactual explanations}
\begin{frame}
  \frametitle{Counterfactual explanations}
  \begin{block}{Definition 1}
      For a classifier \(f\), a metric \(\mu : \mathbb{R}^d \times \mathbb{R}^d \rightarrow \mathbb{R}\) and an input \(x\in \mathcal{E} \subseteq \mathbb{R}^d\), an optimal counterfactual explanation \(x^{\prime}\) is a solution of the following optimization problem:
      \begin{align*}
           x^{\prime} = \mathcal{O}_\mu (x,f,\mathcal{E}) \in \text{argmin}_{x^{\prime} \in \mathcal{E}} & ~~\mu(x,x^{\prime})\\
          \text{s.t.} ~~f(x) &\neq f(x^{\prime})
      \end{align*}
  \end{block}
\textit{\footnotesize Note that for the next examples and the experimental results we use \(\mathnormal{l}_2\)-norm, \(\mu = \lVert.\rVert_2\)}
\end{frame}
\begin{frame}
  \frametitle{Counterfactual explanation example}
  \begin{center}   
    \input{exampleCF.tex}
  \end{center}
\end{frame}

%%% Model extraction attacks. 
\subsection{Model extraction attacks}
\begin{frame}
  \frametitle{Model extraction attacks}
  \begin{figure}
    \centering
    \begin{tikzpicture}[tight background] 
      % Draw the node with the background image
      \node[draw, rounded corners, fill=blue!50, opacity=0.1, minimum width=2.4cm, minimum height=1.6cm, align=center] (MLaaS) at (-4,0) {
          MLaaS\\
          \vspace{3.9cm}\\
          \tiny 1.5\$/1M token : Chat-GPT \\
          \tiny \$0.5-\$1/1K queries Google Sentiment Analysis
        };
      
      % Overlay the text content on top of the background image within the same node
      \begin{scope}[on background layer]
        
      \node[draw, rounded corners, minimum width=2.4cm, minimum height=1.6cm, align=center] (MLaaS) at (-4,0) {
          MLaaS\\
          \includegraphics[width=0.2\textwidth]{figs/model.pdf}
          \\
          \tiny 1.5\$/1M token : Chat-GPT \\
          \tiny \$0.5-\$1/1K queries Google Sentiment Analysis
     };
      
      \end{scope}
      
      % Draw the node
      \node[draw, rounded corners, minimum width=2.52cm, minimum height=1.2cm, align=center, fill=blue!50, opacity=0.1, right, xshift=2cm] (Users) {
        Users\\
        \vspace{3cm}
      }; 
      \begin{scope}[on background layer]
        
      \node[draw, rounded corners, minimum width=2.52cm, minimum height=1.2cm, align=center] at (Users.center) {
        Users\\
        \vspace{1.5cm}
        \only<1-3>{\includegraphics[width=0.13\textwidth]{figs/Sample_User_Icon.png}}
        \only<4>{\vspace{1.5cm}}
      }; 
    \only<4>{\node[ rounded corners, minimum width=2.52cm, minimum height=1.2cm, align=center] at (Users.center) {
        \only<4>{Users \\ }
        \only<4>{\includegraphics[width=0.14\textwidth]{figs/hacker-logo.jpg}\\}
        \only<4>{\includegraphics[width=0.07\textwidth]{figs/model.pdf}}\\
      }; }
      \end{scope}
      
      % The arrows
      \only<2->{\draw[->, thick] ([yshift=0.5cm]Users.west) -- ([yshift=0.5cm]MLaaS.east) node[midway, above] {$\mathbf{x} \in \mathbb{R}^d$};}
      \only<3->{\draw[->, thick] ([yshift=-0.5cm]MLaaS.east) -- ([yshift=-0.5cm]Users.west)
      node[midway, below] {$y = f(\mathbf{x})$};}
      
    \end{tikzpicture}
    \caption{Model extraction attacks framework.}
  \end{figure}
\end{frame}

\begin{frame}
  \frametitle{Attack framework}
    \begin{figure}
    \centering
    \begin{tikzpicture}[tight background] 
      % Draw the node with the background image
      \node[draw, rounded corners, fill=blue!50, opacity=0.1, minimum width=2.4cm, minimum height=1.6cm, align=center] (MLaaS) at (-5,0) {
          MLaaS\\
          \vspace{3.9cm}\\ 
          \tiny 1.50\$/1M token : Chat-GPT \\
          \tiny \$0.5-\$1/1K queries Google Sentiment Analysis
        };
      
      \begin{scope}[on background layer]
      \node[draw, rounded corners, minimum width=2.4cm, minimum height=1.6cm, align=center] (MLaaS1) at (-5,0) {
          MLaaS\\
          \only<1>{\includegraphics[width=.2\textwidth]{figs/model.pdf}}
          \only<2>{ \vspace{0.6cm} \\ \includegraphics[width=.26\textwidth]{figs/DTlogo.png} \\ \vspace{0.5cm}}\\
          \tiny 1.50\$/1M token : Chat-GPT \\
          \tiny \$0.5-\$1/1K queries Google Sentiment Analysis
      };
      
      \end{scope}

      % Draw the 'Users' node
      \node[draw, rounded corners, minimum width=2.52cm, minimum height=1.2cm, align=center, fill=blue!50, opacity=0.1, right, xshift=1cm] (Users) {
        Users\\
        \vspace{3cm}
      }; 
        \begin{scope}[on background layer]
      \node[draw, rounded corners, minimum width=2.52cm, minimum height=1.6cm, align=center] at (Users.center) {
        Users\\ \only<1,2>{\includegraphics[width=.15\textwidth]{figs/hacker-logo.jpg}\\}
        %\only<3>{\\ \vspace{}} %\includegraphics[width=.05\textwidth]{figs/julien.jpeg}\includegraphics[width=.048\textwidth]{figs/Awa.jpeg}\includegraphics[width=.045\textwidth]{figs/thibaut.jpeg} \\\vspace{0.2cm}}
        \only<1>{\includegraphics[width=.09\textwidth]{figs/model.pdf}}
        \only<2>{\vspace{0.3cm}\includegraphics[width=.13\textwidth]{figs/DTlogo.png}\\}
      }; 
      
      \end{scope}
      \draw[->, thick] ([yshift=0.5cm]Users.west) -- ([yshift=0.5cm]MLaaS.east) node[midway, above] {$\mathbf{x} \in \mathbb{R}^d$\only<2>{$, \mathcal{E} \subseteq \mathbb{R}^d$}};
      \draw[->, thick] ([yshift=-0.5cm]MLaaS.east) -- ([yshift=-0.5cm]Users.west) node[midway, below] {$y = f(\mathbf{x})$};
      \only<1->{\draw[->, thick] ([yshift=-0.5cm]MLaaS.east) -- ([yshift=-0.5cm]Users.west) node[midway, above] {$x' = \mathcal{O}_\mu (f,\mathbf{x}, \mathcal{E})$};}
    
    
    \end{tikzpicture}
    \caption{\only<1>{Model}\only<2->{Trees ensembles} extraction attacks framework.}
  \end{figure}
\end{frame}

\begin{frame}
  \frametitle{Functionally Equivalent Attacks}
  \begin{block}{Definition 2}
    A functionally equivalent attack is an attack that aims to create a model that is functionally equivalent to the target model, meaning it produces the same outputs for any inputs, i.e., for all \(\mathbf x \in \mathcal{X}\), \(f'(\mathbf x) = f(\mathbf x)\).
  \end{block}
  \only<2>{\begin{block}{Definition 3}
    Let \(f\) be the target model and \(\hat{f}\) be the extracted model. The fidelity of the extracted model over a dataset \(\mathcal{D}\) is defined as:
    \[
    \mathcal{F}(\hat{f},f) = \frac{1}{|\mathcal{D}|} \sum_{\mathbf{x} \in \mathcal{D}} \mathbb{I}[\hat{f}(\mathbf{x}) = f(\mathbf{x})]
    \]
    where \(\mathbb{I}\) is the indicator function. A functionally equivalent attack has \(\mathcal{F}(\hat{f},f) = 1\).
  \end{block}}
\end{frame}

\AtBeginSection[]
{
  \begin{frame}
    \frametitle{Outline}
    \tableofcontents[currentsection]
  \end{frame}
}


\section{Project 1 --- From Counterfactuals to Trees: Competitive Analysis of Model Extraction Attacks}
\subsection*{Related Work}
\subsubsection*{Surrogate attacks}
\begin{frame}
  \frametitle{Surrogate attacks}
  \begin{minipage}{0.55\textwidth}
    \scriptsize
    \begin{itemize}
      \item<1-> \textbf{Surrogate attacks}: Create a dataset and train a surrogate model (MLP, RF, DT\ldots) on it.
      \item<2-> \textbf{CF surrogate}: [\textit{Aïvodji et al., 2020}] Create a surrogate training set by querying the model uniformly (or from an existing dataset) 
      \[
      \mathcal{D}_S=\{(x,y), (x',y') \quad | x' = \mathcal{O}_\mu (f,x,\mathcal{E})\} 
      \]
      \item<3-> \textbf{DualCF surrogate}: [\textit{Wang et al., 2022}] Same as \textbf{CF surrogate} but also takes the counterfactuals of the counterfactuals.
      \\
      \begin{align*}
        \mathcal{D}_S=\{(x,y) &, (x',y'), (x'',y'') |\\
           x' &= \mathcal{O}_\mu (f,x,\mathcal{E}) \text{ and } \\
           x'' &= \mathcal{O}_\mu (f,x',\mathcal{E})\} 
      \end{align*} 
    \end{itemize}
  \end{minipage}
  \begin{minipage}{0.4\textwidth}
    \only<2>{\begin{figure}
      \centering
      \includegraphics[width=0.8\textwidth]{figs/CF-DualCF.png}
      \caption{CF surrogate attack (from \textit{Wang et al., 2022}). Dashed: surrogate.}
    \end{figure}}
    \only<3->{\begin{figure}
      \centering
      \includegraphics[width=0.8\textwidth]{figs/DualCF.png}
      \caption{DualCF surrogate attack (from \textit{Wang et al., 2022}). Dashed: surrogate.}
    \end{figure}}
  \end{minipage}  
\end{frame}


\subsubsection*{Steal-ML}
\begin{frame}
  \frametitle{Steal-ML}
  \begin{minipage}{0.55\textwidth}
  \scriptsize
  \textbf{Steal-ML} [\textit{Tramèr et al., 2016}]
    \begin{itemize}
      \item Functionally equivalent (i.e., same decision boundary) model extraction attack.
      \item Use other type of information (node id) and not counterfactual explanation. 
      \item Only for decision trees.
    \end{itemize}
  \end{minipage}
  \begin{minipage}{0.4\textwidth}
    \begin{figure}
      \centering
      \includegraphics[width=0.8\textwidth]{figs/PathFinding.png}
      \caption{PathFinding (Steal-ML) attack (from \textit{Tramèr et al., 2016}).}
    \end{figure}
  \end{minipage} 
\end{frame}
\subsection*{Tree Reconstruction Attack (TRA)}
\begin{frame}[fragile]
  \frametitle{Tree Reconstruction Attack}
  \hspace{0.3cm}
  \begin{minipage}{0.55\textwidth}
        \scriptsize
        \setcounter{algocf}{0}
        \begin{algorithm}[H]
        \SetAlgoLined{}
        \uncover<1->{\alert<1>{\textbf{Require} A model \(f\), a counterfactual explanation oracle \(\mathcal{O}_\mu{}\), a node \(node\) (root node in the beginning), and a bounded input space \(\mathcal{E} \subset \mathbb{R}^d\)}}
        
        \vspace{0.3cm}
        \uncover<2->{\alert<2>{\(x \gets \text{center}(\mathcal{E})\) \\ }}
        \uncover<3->{\alert<3>{\(x', y \gets \mathcal{O}_\mu(f,x,\mathcal{E}), f(x)\) \\ }}
        \uncover<7->{\alert<7>{\If{\(x' \text{does not exists}\)}{
            \(node \gets leaf(y)\) \\
            \textbf{return}\\
        }}}
        \uncover<4->{\alert<4>{\(i, v \gets \{(i, x'_i) , x_i \neq x'_i\} \) \\ }}
        \uncover<4->{\alert<4>{\(node \gets node(i, v)\) \\ }}
        \uncover<4->{\alert<4>{\(\mathcal{E}_1, \mathcal{E}_2 \gets \text{split}(\mathcal{E},node)\) \\ }}
        \uncover<5->{\alert<5>{\textbf{TRA} (\(f, \mathcal{O}_\mu, node.left, \mathcal{E}_1\)) \\ }}
        \uncover<5->{\alert<5>{\textbf{TRA} (\(f, \mathcal{O}_\mu, node.right, \mathcal{E}_2\)) \\ }}
        
         \caption{TRA algorithm}
        \end{algorithm}
  
    \end{minipage}
    \begin{minipage}{0.4\textwidth}
         \centering
         % Graph
        
        \begin{tikzpicture}[scale=2.9]
            % Dessiner les axes
            \draw[->] (0,0) -- (1.05,0) node[right] {$x_1$};
            \draw[->] (0,0) -- (0,1.05) node[above] {$x_2$};

            % Ajouter les lignes
            \draw[thick] (-0.02,0.7) -- (1,0.7) node[left, xshift=-7.5em] {\tiny $0.7$};
            \draw[thick] (0.6,-0.02) -- (0.6,1) node[below, yshift=-7.5em] {\tiny $0.6$};
            \node at (-0.05,-0.05) {\tiny $0$};
            
            % Colorer les zones
            \fill[red!70] (0,0.7) rectangle (0.6,1);
            \fill[red!70] (0.6,0) rectangle (1,0.7);
            \fill[blue!70] (0,0) rectangle (0.6,0.7);
            \fill[blue!70] (0.6,0.7) rectangle (1,1);

            \draw[thick] (-0.02,1.0) -- (1.0,1.0) node[left, xshift=-7.5em] {\tiny $1$};
            \draw[thick] (1.0,-0.02) -- (1.0,1.0) node[below, yshift=-7.5em] {\tiny $1$};
        \end{tikzpicture}
        \\
        \begin{tikzpicture}[scale=2.9]
            % Dessiner les axes
            \draw[->] (0,0) -- (1.05,0) node[right] {$x_1$};
            \draw[->] (0,0) -- (0,1.05) node[above] {$x_2$};

            % Ajouter les lignes
            \only<6->{\draw[thick] (-0.02,0.7) -- (1,0.7) node[left, xshift=-7.5em] {\tiny $0.7$};}
            \only<4->{\draw[thick] (0.6,-0.02) -- (0.6,1) node[below, yshift=-7.5em] {\tiny $0.6$};}
            \node at (-0.05,-0.05) {\tiny $0$};
            
            % Colorer les zones
            \only<7->\fill[red!70] (0,0.7) rectangle (0.6,1);
            \only<7->\fill[red!70] (0.6,0) rectangle (1,0.7);
            \only<7->\fill[blue!70] (0,0) rectangle (0.6,0.7);
            \only<7->\fill[blue!70] (0.6,0.7) rectangle (1,1);

            \draw[thick] (-0.02,1.0) -- (1.0,1.0) node[left, xshift=-7.5em] {\tiny $1$};
            \draw[thick] (1.0,-0.02) -- (1.0,1.0) node[below, yshift=-7.5em] {\tiny $1$};

            % 1st query 
            \only<2-4>{\node at (0.5, 0.5) {\tiny $x$};}
            \only<3-4>{\node at (0.6, 0.5) {\tiny \textcolor{green}{$x$}};}

            % 2nd and 3rd queries 
            \only<5>{\node at (0.3, 0.5) {\tiny $x$};}
            \only<5>{\node at (0.3, 0.7) {\tiny \textcolor{green}{$x$}};} 
            \only<5>{\node at (0.8, 0.5) {\tiny $x$};}
            \only<5>{\node at (0.8, 0.7) {\tiny \textcolor{green}{$x$}};}

            %leaf node queries
            \only<6>{\node at (0.3, 0.35) {\tiny $x$};}
            \only<6>{\node at (0.3, 0.85) {\tiny $x$};}
            \only<6>{\node at (0.8, 0.35) {\tiny $x$};}
            \only<6>{\node at (0.8, 0.85) {\tiny $x$};}
            
        \end{tikzpicture}
        
    \end{minipage}
\end{frame}

\begin{frame}
    \frametitle{TRA improvements}
    
    \begin{minipage}{0.5\textwidth}
        \scriptsize
        \begin{itemize}
            \uncover<1->{\alert<1>{\item For corner counterfactual explanation.}}
            \uncover<2->{\alert<2->{\item \textbf{TRA+}: Reorganizing the splits order using an estimation of the Weighted Gini Impurity.} }
        \end{itemize}
    \end{minipage}
    \begin{minipage}{0.35\textwidth}
         \centering
         % Graphe
        \only<1->{
          \begin{tikzpicture}[scale=4.5]
            % Dessiner les axes
            \draw[->] (0,0) -- (1.05,0) node[right] {$x_1$};
            \draw[->] (0,0) -- (0,1.05) node[above] {$x_2$};
            
            % Colorer les zones
            \fill[red!70] (0.6,0.7) rectangle (1,1);
            \fill[blue!70] (0,0) rectangle (0.6,1.0);
            
            % Ajouter les lignes
            \draw[thick] (0.6,-0.02) -- (0.6,1) node[below, yshift=-12em] {$0.6$};
            \node at (-0.05,-0.05) {$0$};
            \draw[thick] (-0.02,0.7) -- (1,0.7) node[left, xshift=-12em] {$0.7$};
            
            \fill[blue!70] (0,0) rectangle (1.0,0.7);
            
            \draw[thick] (-0.02,1.0) -- (1.0,1.0) node[left, xshift=-12em] {$1$};
            \draw[thick] (1.0,-0.02) -- (1.0,1.0) node[below, yshift=-12em] {$1$};
            
            
            % 1st query 
            \node at (0.5, 0.5) {\tiny $x$};
            \node at (0.6, 0.7) {\tiny \textcolor{green}{$x$}};
          \end{tikzpicture}
        }
    \end{minipage}
\end{frame}

\section*{Results}

\subsection*{Theoretical results}


\begin{frame}
\frametitle{Functionally equivalent}
  % Your content here
    \begin{block}{Proposition 1}
        \tiny Denote \(\hat{f}\) the extracted tree of \(f\) using \textbf{TRA}. Then, we have that:
        \[
        \forall x \in \mathcal{E}, ~~ \hat{f}(x) = f(x) 
        \]
        i.e. \textbf{TRA} is a functionally equivalent attack.
    \end{block} \pause{}
    \begin{block}{Proposition 2}
        \tiny Let \(n\) denote the num‡ber of different decision nodes in the tree and \(d\) denote the number of dimensions with at least one split level. Then the worst case complexity of \textbf{TRA} is \(O\left((1 + \frac{n}{d}){}^d\right)\).
    \end{block}
\end{frame}



\begin{frame}
\frametitle{C-competitiveness}
  % Your content here
    \begin{block}{Definition 3}
        \tiny An algorithm \(\mathcal{A}\) is \(C_\mathcal{A}\)\textbf{-competitive} if, for any given decision tree \(f\):
        \[ Q_{\mathcal{A}}^f \leq C_\mathcal{A} \cdot Q_{opt}^f \]
        i.e. \(C_\mathcal{A} = \underset{f}{\sup}\left(\frac{Q_{\mathcal{A}}^f}{Q_{opt}^f}\right) \), where \(Q_{\mathcal{A}}^f\) (resp., \(Q_{opt}^f\)) is the number of queries of \(\mathcal{A}\) (resp., the optimal algorithm) to reconstruct \(f\).
    \end{block}
    \only<2->{\begin{block}{Proposition 3}
        \tiny For all \(n> d \geq 1\), there exists \textbf{no divide and conquer algorithm} (D\&C) that has a better competitive ratio than \(C_{TRA}\).
    \[
    C_{TRA} = \frac{2(1 + \frac{n}{d}){}^d - 1}{2n + 1}
    \]
    \end{block}}
    \only<3->{\begin{block}{Proposition 4}
      \tiny For all \(d \geq 1\), there exists \textbf{no algorithm} that has a better competitive ratio \(C_\mathcal{A}\) than \(d^2\).
    \end{block}}
\end{frame}

\subsection*{Experimental results}
\begin{frame}
  \frametitle{Experimental Setup}
  \begin{itemize}
    \item \textbf{Attacks}: TRA (ours), PathFinding (\textit{Tramèr et al., 2016}), CF (\textit{Aïvodji et al., 2020}) and DualCF (\textit{Wang et al., 2022}).
    \item \textbf{Datasets}: Moons, Adult, COMPAS, German Credit.
    \item \textbf{Models}: Decision Trees with variable depth (4 to 10 and None). Trained over 5 seeds.
    \item \textbf{Fidelity}: Percentage of correct predictions over a uniformly sampled test set (\(\mathcal{D}_T\)) of 3000 samples. 
    \[
     Fidelity(\hat{f}) = \frac{1}{|\mathcal{D}_T|} \sum_{x \in \mathcal{D}_T} \mathds{1}_{\hat{f}(x) = f(x)}
    \] 
    \item Note that for the surrogate attacks we create a dataset of 100\(\times{}\)number of nodes in the tree and we use decision trees (DT) as surrogates.
  \end{itemize}
  
\end{frame}

\begin{frame}
  \frametitle{Experimental Results}
  \begin{table}
    \centering
    \tiny
    \begin{tabular}{cccccccc}
      \toprule
      \textbf{Dataset} & \textbf{Features} & \textbf{Data Points} & \textbf{Classes} & \textbf{Nodes} & \textbf{Queries}  & \textbf{Fidelity} & \textbf{Cost} \\
        & \(d\) &  &  &  & TRA &  & \( \$ 1 /1K\) \\
      \midrule
      Moons           & 2  & 100   & 2 & 20.6   & \textbf{41.2} & 100\% & \(< \$1 \) \\
      Adult           & 41 & 45222 & 2 & 236.09   & \textbf{13918.75} & 100\% & \( \$13.9 \) \\
      COMPAS          & 5  & 5278  & 2 & 436.73 & \textbf{229.8} & 100\% & \(< \$1 \)   \\
      German Credit   &  19 &  1000  &  2 &  23.56 &  \textbf{59.0} &  100\% &  \(< \$1 \) \\
      \bottomrule
    \end{tabular}\label{tab:tab_2_results}
    \captionsetup{font=tiny}
    \caption{Summary of the TRA algorithm performance across various datasets. Cost are calculated considering \( \$ 1\) per \(1000\) queries.}
  \end{table}
\end{frame}

\begin{frame}
  \frametitle{Experimental Results}
  \begin{figure}
    \begin{subfigure}{.5\textwidth}
      \centering
      \includegraphics[width=.8\linewidth]{figs/P4_COMPAS.pdf}
      \caption{COMPAS dataset.}\label{fig:sfig1}
    \end{subfigure}%
    \begin{subfigure}{.5\textwidth}
      \centering
      \includegraphics[width=.8\linewidth]{figs/P4_GCredit.pdf}
      \caption{German Credit dataset.}\label{fig:sfig2}
    \end{subfigure}
    \caption{Plots of number of queries vs number of nodes for the COMPAS and German Credit datasets.}\label{fig:figTRAvsPathFinding}
    \end{figure}
\end{frame}

\begin{frame}
  \frametitle{Experimental Results}
  \begin{figure}
    \begin{subfigure}{.5\textwidth}
      \centering
      \includegraphics[width=.7\linewidth]{figs/P5_COMPAS.pdf}\label{fig:2sfig1}
    \end{subfigure}%
    \begin{subfigure}{.5\textwidth}
      \centering
      \includegraphics[width=.7\linewidth]{figs/P5_GCredit.pdf}\label{fig:2sfig2}
    \end{subfigure}\\
    \begin{subfigure}{.5\textwidth}
      \centering
      \includegraphics[width=.7\linewidth]{figs/P5_Adult.pdf}\label{fig:2sfig3}
    \end{subfigure}%
    \begin{subfigure}{.5\textwidth}
      \centering
      \includegraphics[width=.7\linewidth]{figs/P5_Moons.pdf}\label{fig:2sfig4}
    \end{subfigure}
    \caption{Plots of means fidelity vs number of nodes.}\label{fig:figTRAvsCFandDualCF}
    \end{figure}
\end{frame}

\AtBeginSection[]
{
  \begin{frame}
    \frametitle{Outline}
    \tableofcontents[currentsection]
  \end{frame}
}

\section{Project 2 --- Learning Voronoi Diagrams for Optimal Counterfactual Explanations in Tree Ensembles}
\begin{frame}
  \frametitle{Framework}
\end{frame}

\begin{frame}
  \frametitle{Geometry of ensemble trees}
\end{frame}

\begin{frame}
  \frametitle{Counterfactual map and Voronoi diagram}
\end{frame}

\begin{frame}
  \frametitle{Project Pipeline}
\end{frame}

\section{Project 3 --- Guaranteeing Local Optimality in Model-Agnostic Counterfactuals}
\begin{frame}
  \frametitle{Framework}
\end{frame}

\begin{frame}
  \frametitle{The idea}
\end{frame}


\AtBeginSection[]
{
  \begin{frame}
    \frametitle{Outline}
    \tableofcontents[currentsection]
  \end{frame}
}
\section{Conclusion and Future works}
\begin{frame}
  \frametitle{Conclusion and Future works}
  \begin{itemize}
     \item TRA is the only functionally equivalent attack that use counterfactual explanations. \pause{}
     \item Very few functionally equivalent extraction attacks exist in the state of the art. \pause{}
     \item Raising awareness of these powerful attacks. \pause{}
     \item No effective defense strategy has yet been devised against these attacks. \pause{}
     \item Find a better lower bound for the competitive ratio of all algorithms.
  \end{itemize}
\end{frame}

\begin{frame}
    \begin{center}   
      \Huge Thanks for your attention!\\
      \vspace{2cm}
      Questions?
    \end{center}
\end{frame}


\begin{frame}
    \frametitle{Annexe}
    \begin{center}   
      \begin{figure}
          \centering
          \includegraphics[width=0.9\linewidth]{figs/gambsResults.png}
          \caption{Model extraction from counterfactual explanations, Aïvodji et al. 2020}\label{fig:Gambs-label}
      \end{figure}
    \end{center}
\end{frame}

\begin{frame}
    \frametitle{Annexe}
    \begin{center}   
      \begin{figure}
          \centering
          \includegraphics[width=0.9\linewidth]{figs/TramerResults.png}
          \caption{Stealing Machine Learning Models via Prediction APIs, Tramer et al. 2016}\label{fig:Tramer-label}
      \end{figure}
    \end{center}
\end{frame}

\begin{frame}
    \frametitle{Annexe}
    \begin{center}   
      \begin{figure}
          \centering
          \includegraphics[width=0.9\linewidth]{figs/CarliniResults.png}
          \caption{Stealing Part of a Production Language Model, Carlini et al. 2024}\label{fig:Carlini-label}
      \end{figure}
    \end{center}
\end{frame}

\begin{frame}
    \frametitle{Annexe}
    \begin{block}{Proposition}
    For any decision tree with \(n > 0\) split levels and  \(d>0\) features, the algorithm TRA is \(C_{TRA}^{(n,d)}\)-competitive with:
    \[
    C_{TRA}^{(n,d)} = \frac{\sum_{i=1}^d k_i \prod_{j=1}^{i-1} (k_j + 1)  + \prod_{j=1}^d (k_j + 1)}{2n+1} \leq \frac{2(1 + \frac{n}{d}){}^d - 1}{2n + 1}
    \]
    \end{block}
\end{frame}

\end{document}