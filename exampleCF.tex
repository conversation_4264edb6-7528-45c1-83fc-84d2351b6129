\begin{figure}[!ht]
        \centering
        \begin{tikzpicture}[scale=4.5]
                % Draw axes
                \draw[->] (0,0) -- (1.05,0) node[right] {$x_1$};
                \draw[->] (0,0) -- (0,1.05) node[above] {$x_2$};
            
                % Add split lines
                \node at (-0.05,-0.05) {$0$};
                \node at (-0.05,1.0) {$1$};
                \node at (1.0,-0.06) {$1$};
                
                % Color regions
                \fill[class1!70] (0,0) rectangle (1,1);
                \fill[class2!70] (0,0) rectangle (0.6,0.7);

                
            
                % Draw vertical split
                \draw (-0.02,0.7) -- (0.02,0.7) node[left, xshift=-0.1cm] {$0.7$};
                \draw (-0.02,1.0) -- (0.02,1.0);
                \draw (0.6,-0.02) -- (0.6,0.02) node[below, yshift=-0.1cm] {$0.6$};
                \draw (1.0,-0.02) -- (1.0,0.02);
                \draw[thick] (0,0.7) -- (1.0,0.7);
                \draw[thick] (0.6,0) -- (0.6,0.7);

                % Points  

                \coordinate (q) at (0.3,0.35);
                \coordinate (optcf) at (0.605,0.35);
                \coordinate (loptcf) at (0.3,0.706);
                \coordinate (valcf) at (0.8,0.8);
                \draw (q) node {$\bullet$};
                \draw (optcf) node {$\times$};
                \draw (loptcf) node {$\times$};
                \draw (valcf) node {$\times$};
                \node[above, yshift=0cm, xshift=-0.65cm] at (q) {\scriptsize ``Query''};
                \node[above, yshift=0cm, xshift=1.0cm] at (optcf) {\scriptsize ``Optimal CF''};
                \node[above, yshift=0.2cm, xshift=0cm] at (loptcf) {\scriptsize ``Locally optimal CF''};
                \node[above, yshift=0.2cm, xshift=0cm] at (valcf) {\scriptsize ``Valid CF''};
        \end{tikzpicture}
        \caption{Example of the different types of counterfactual explanations (CF) quality (for \(\mu =L_2\)) for the previous tree (Figure~\ref{fig:DTexample}).}\label{fig:CFexample}
    \end{figure}