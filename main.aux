\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyperFirstAtBeginDocument{\AtBeginDocument}
\HyperFirstAtBeginDocument{\ifx\hyper@anchor\@undefined
\global\let\oldcontentsline\contentsline
\gdef\contentsline#1#2#3#4{\oldcontentsline{#1}{#2}{#3}}
\global\let\oldnewlabel\newlabel
\gdef\newlabel#1#2{\newlabelxx{#1}#2}
\gdef\newlabelxx#1#2#3#4#5#6{\oldnewlabel{#1}{{#2}{#3}}}
\AtEndDocument{\ifx\hyper@anchor\@undefined
\let\contentsline\oldcontentsline
\let\newlabel\oldnewlabel
\fi}
\fi}
\global\let\hyper@last\relax 
\gdef\HyperFirstAtBeginDocument#1{#1}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\providecommand \oddpage@label [2]{}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{1}{1/1}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {1}{1}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{2}{2/2}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {2}{2}}}
\@writefile{toc}{\beamer@sectionintoc {1}{Background}{3}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\sectionentry {1}{Background}{3}{Background}{0}}}
\@writefile{nav}{\headcommand {\slideentry {1}{0}{1}{3/3}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {3}{3}}}
\@writefile{toc}{\beamer@subsectionintoc {1}{1}{Decision Trees}{4}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {3}{3}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{1}{1}{4}{Decision Trees}}}
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{fig:DTexample}{{1}{4}{A decision tree and its corresponding decision boundary on a two-dimensional feature space.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:DTexample}{4}}
\@writefile{nav}{\headcommand {\slideentry {1}{1}{1}{4/4}{Decision Trees}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {4}{4}}}
\@writefile{nav}{\headcommand {\beamer@partpages {1}{4}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {5}{4}}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {5}{4}}}
\@writefile{nav}{\headcommand {\beamer@documentpages {4}}}
\@writefile{nav}{\headcommand {\gdef \inserttotalframenumber {5}}}
\gdef \@abspage@last{4}
