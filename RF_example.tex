% define a command for one tree
\newcommand{\tree}[1]{\begin{tikzpicture}[
                    sibling distance=3em,
                    level distance=2em,
                    every node/.style = {shape=rectangle, rounded corners, draw, align=center, top color=white, font=\tiny},
                    blue node/.style = {bottom color=class2!70, shape=circle},
                    red node/.style = {bottom color=class1!70, shape=circle},
                ]
                    \node {\(x_2 \leq 0.7\)}
                        child { node[xshift=-0.5em] {\(x_1 \leq 0.6\)}
                            child {node[blue node] {\classA}}
                            child {node[red node, xshift=-0.2em] {\classB}}
                        }
                        child {node[red node] {\classB}};
                \end{tikzpicture}}

\begin{figure}
        \centering
        \begin{subfigure}{0.4\textwidth}
            \centering
            \begin{subfigure}{0.45\textwidth}
                \centering
                \tree{1}
            \end{subfigure}
            \hfill
            \begin{subfigure}{0.45\textwidth}
                \centering
                \tree{2}
            \end{subfigure}

            \vspace{0.3cm}

            \begin{subfigure}{0.45\textwidth}
                \centering
                \tree{3}
            \end{subfigure}
            \hfill
            \begin{subfigure}{0.45\textwidth}
                \centering
                \tree{4}
            \end{subfigure}
            \vspace{0.3cm}
            \caption{An ensemble of four decision trees.}
        \end{subfigure}
        \hfill
        \begin{subfigure}{0.5\textwidth}
            \centering
            \begin{tikzpicture}[scale=4]
                % Draw axes
                \draw[->] (0,0) -- (1.05,0) node[right] {$x_1$};
                \draw[->] (0,0) -- (0,1.05) node[above] {$x_2$};
            
                % Add split lines
                \node at (-0.05,-0.05) {$0$};
                \node at (-0.05,1.0) {$1$};
                \node at (1.0,-0.06) {$1$};
                
                % Color regions
                \fill[class1!70] (0,0) rectangle (1,1);
                \fill[class2!70] (0,0) rectangle (0.6,0.7);
            
                % Draw vertical split
                \draw (-0.02,0.7) -- (0.02,0.7) node[left, xshift=-0.1cm] {$0.7$};
                \draw (-0.02,1.0) -- (0.02,1.0);
                \draw (0.6,-0.02) -- (0.6,0.02) node[below, yshift=-0.1cm] {$0.6$};
                \draw (1.0,-0.02) -- (1.0,0.02);
                \draw[thick] (0,0.7) -- (1.0,0.7);
                \draw[thick] (0.6,0) -- (0.6,0.7);
                
                \node at (0.3,0.35) {$c_2$};
                \node at (0.8,0.35) {$c_1$};
                \node at (0.5,0.85) {$c_1$};
            \end{tikzpicture}
            \caption{Decision Boundary of the decision tree.}
        \end{subfigure}
        \caption{A decision tree and its corresponding decision boundary on a two-dimensional feature space.}\label{fig:RFexample}
    \end{figure}